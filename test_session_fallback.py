#!/usr/bin/env python3
"""
测试会话降级模型功能
验证一旦使用降级模型，后续请求都使用相同的降级模型
"""

import requests
import json
import time

def test_session_fallback_behavior():
    """测试会话降级模型行为"""
    print("🧪 Testing Session Fallback Model Behavior")
    print("=" * 60)
    print("This test verifies that once a fallback model is used,")
    print("all subsequent requests in the session use the same fallback model.")
    print()
    
    # 测试请求配置
    base_request = {
        "model": "claude-sonnet-4",
        "messages": [
            {
                "role": "user",
                "content": "Test message for session fallback behavior"
            }
        ],
        "max_tokens": 50,
        "temperature": 0.7
    }
    
    results = []
    
    print("📤 Sending multiple requests to test session behavior...")
    print(f"🎯 All requests ask for: {base_request['model']}")
    print()
    
    # 发送多个请求
    for i in range(5):
        print(f"📤 Request {i+1}/5...")
        
        try:
            response = requests.post(
                "http://localhost:8080/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer test-token"
                },
                json=base_request,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                model_used = result.get('model', 'unknown')
                results.append({
                    'request': i+1,
                    'requested': base_request['model'],
                    'actual': model_used,
                    'status': 'success'
                })
                print(f"   ✅ Success: {model_used}")
            else:
                results.append({
                    'request': i+1,
                    'requested': base_request['model'],
                    'actual': 'error',
                    'status': f'error_{response.status_code}',
                    'error': response.text[:100]
                })
                print(f"   ❌ Error {response.status_code}: {response.text[:50]}...")
                
        except Exception as e:
            results.append({
                'request': i+1,
                'requested': base_request['model'],
                'actual': 'exception',
                'status': 'exception',
                'error': str(e)[:100]
            })
            print(f"   ❌ Exception: {e}")
        
        # 短暂延迟
        time.sleep(1)
    
    return results

def analyze_session_behavior(results):
    """分析会话行为"""
    print("\n📊 Session Behavior Analysis")
    print("=" * 60)
    
    # 统计使用的模型
    models_used = []
    for result in results:
        if result['status'] == 'success':
            models_used.append(result['actual'])
    
    if not models_used:
        print("❌ No successful requests to analyze")
        return
    
    print("📋 Request Summary:")
    for i, result in enumerate(results, 1):
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"   {i}. {status_icon} Requested: {result['requested']} → Actual: {result['actual']}")
    
    print()
    print("🔍 Analysis:")
    
    # 检查是否发生了模型降级
    unique_models = list(set(models_used))
    requested_model = results[0]['requested']
    
    if len(unique_models) == 1:
        used_model = unique_models[0]
        if used_model == requested_model:
            print(f"✅ Consistent behavior: All requests used {used_model}")
            print("   No fallback was triggered (no rate limits encountered)")
        else:
            print(f"✅ Session fallback working correctly!")
            print(f"   All requests consistently used fallback model: {used_model}")
            print(f"   Original requested model: {requested_model}")
            print("   This indicates the session fallback mechanism is working")
    else:
        print(f"⚠️  Inconsistent behavior detected:")
        print(f"   Multiple models used: {unique_models}")
        print("   Expected: All requests should use the same model once fallback is triggered")
        
        # 检查是否有降级模式
        if requested_model not in models_used:
            print("   It appears rate limiting occurred, but model usage is inconsistent")
        else:
            print("   Mixed usage between original and fallback models")

def test_different_model_requests():
    """测试不同模型请求的行为"""
    print("\n🔄 Testing Different Model Requests")
    print("=" * 60)
    print("Testing how the session handles requests for different models")
    print("when a fallback model is already active.")
    print()
    
    test_models = [
        "claude-sonnet-4",
        "claude-3.7-sonnet", 
        "claude-3.5-sonnet",
        "gpt-4"
    ]
    
    results = []
    
    for model in test_models:
        print(f"📤 Testing with model: {model}")
        
        request_data = {
            "model": model,
            "messages": [{"role": "user", "content": f"Test with {model}"}],
            "max_tokens": 30
        }
        
        try:
            response = requests.post(
                "http://localhost:8080/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer test-token"
                },
                json=request_data,
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                actual_model = result.get('model', 'unknown')
                results.append((model, actual_model, 'success'))
                print(f"   ✅ Requested: {model} → Actual: {actual_model}")
            else:
                results.append((model, 'error', f'error_{response.status_code}'))
                print(f"   ❌ Error {response.status_code}")
                
        except Exception as e:
            results.append((model, 'exception', 'exception'))
            print(f"   ❌ Exception: {e}")
        
        time.sleep(0.5)
    
    print("\n📊 Different Model Request Analysis:")
    successful_results = [(req, act) for req, act, status in results if status == 'success']
    
    if successful_results:
        actual_models_used = [act for req, act in successful_results]
        unique_actual_models = list(set(actual_models_used))
        
        if len(unique_actual_models) == 1:
            fallback_model = unique_actual_models[0]
            print(f"✅ Session fallback is working correctly!")
            print(f"   All requests used the same model: {fallback_model}")
            print("   Regardless of what model was requested")
        else:
            print(f"⚠️  Inconsistent behavior:")
            print(f"   Multiple models used: {unique_actual_models}")

def show_expected_behavior():
    """显示期望的行为"""
    print("\n📚 Expected Session Fallback Behavior")
    print("=" * 60)
    print("🔄 Normal flow:")
    print("   1. Request claude-sonnet-4 → Success with claude-sonnet-4")
    print("   2. Request claude-sonnet-4 → Success with claude-sonnet-4")
    print("   3. Request claude-sonnet-4 → Success with claude-sonnet-4")
    print()
    print("🔄 With rate limiting:")
    print("   1. Request claude-sonnet-4 → Rate limit → Fallback to claude-3.7-sonnet")
    print("   2. Request claude-sonnet-4 → Use session fallback → claude-3.7-sonnet")
    print("   3. Request claude-sonnet-4 → Use session fallback → claude-3.7-sonnet")
    print("   4. Request gpt-4 → Use session fallback → claude-3.7-sonnet")
    print()
    print("✅ Key benefits:")
    print("   • Avoids repeated rate limit errors")
    print("   • Consistent performance within session")
    print("   • Transparent to the user")

def main():
    """主函数"""
    print("🎯 Session Fallback Model Test")
    print("=" * 60)
    print("This test verifies that once a fallback model is used due to rate limits,")
    print("all subsequent requests in the session continue to use that fallback model.")
    print()
    
    # 显示期望行为
    show_expected_behavior()
    
    # 等待用户确认
    input("\nPress Enter to start testing...")
    
    try:
        # 测试基本会话行为
        results = test_session_fallback_behavior()
        
        # 分析结果
        analyze_session_behavior(results)
        
        # 测试不同模型请求
        test_different_model_requests()
        
        print("\n🎉 Session Fallback Testing Completed!")
        print("=" * 60)
        print("Check the server logs to see detailed session management.")
        print("Look for messages like:")
        print('  "Session fallback model set to: claude-3.7-sonnet"')
        print('  "Using session fallback model: claude-3.7-sonnet"')
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
