#!/usr/bin/env python3
"""
演示 429 错误处理和模型降级功能
"""

import requests
import json
import time

def simulate_rate_limit_scenario():
    """模拟速率限制场景"""
    print("🎭 Simulating Rate Limit Scenario")
    print("=" * 50)
    print("This demo shows how the system handles rate limits:")
    print("1. 🚀 Initial request with Claude 4")
    print("2. ⚠️  Simulated 429 error")
    print("3. 🔄 Automatic downgrade to Claude 3.7")
    print("4. ✅ Successful response with fallback model")
    print()
    
    # 模拟请求
    request_data = {
        "model": "claude-sonnet-4",
        "messages": [
            {
                "role": "user",
                "content": "Explain the concept of rate limiting in APIs and how to handle it gracefully."
            }
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    print("📤 Sending request to OpenAI-compatible endpoint...")
    print(f"🎯 Requested model: {request_data['model']}")
    print(f"💬 Message: {request_data['messages'][0]['content'][:50]}...")
    print()
    
    try:
        response = requests.post(
            "http://localhost:8080/v1/chat/completions",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer demo-token"
            },
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"🤖 Model used: {result.get('model', 'unknown')}")
            print(f"📝 Response preview: {result.get('choices', [{}])[0].get('message', {}).get('content', '')[:100]}...")
            print()
            print("🔍 Full response structure:")
            print(json.dumps(result, indent=2)[:500] + "...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Error details: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - this might indicate rate limiting or server issues")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - make sure the server is running on localhost:8080")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def demonstrate_model_fallback():
    """演示模型降级逻辑"""
    print("\n🔄 Demonstrating Model Fallback Logic")
    print("=" * 50)
    
    models_to_test = [
        "claude-sonnet-4",
        "claude-3.7-sonnet", 
        "claude-3.5-sonnet",
        "gpt-4",
        "unknown-model"
    ]
    
    for model in models_to_test:
        print(f"\n🧪 Testing with model: {model}")
        
        request_data = {
            "model": model,
            "messages": [{"role": "user", "content": f"Hello from {model}!"}],
            "max_tokens": 50
        }
        
        try:
            response = requests.post(
                "http://localhost:8080/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer demo-token"
                },
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                actual_model = result.get('model', 'unknown')
                print(f"  ✅ Requested: {model} → Actual: {actual_model}")
            else:
                print(f"  ❌ Error {response.status_code}: {response.text[:50]}...")
                
        except Exception as e:
            print(f"  ❌ Failed: {e}")
        
        time.sleep(1)  # 避免过快请求

def show_error_handling_info():
    """显示错误处理信息"""
    print("\n📚 Rate Limit Error Handling Information")
    print("=" * 50)
    print("Our system handles the following scenarios:")
    print()
    print("🔴 429 Too Many Requests:")
    print("   → Automatically downgrades to claude-3.7-sonnet")
    print("   → Retries with the fallback model")
    print()
    print("🔴 500 Internal Server Error (with 429 content):")
    print("   → Detects '429 Too Many Requests' in error message")
    print("   → Treats as rate limit and downgrades model")
    print()
    print("🔄 Fallback Chain:")
    print("   claude-sonnet-4 → claude-3.7-sonnet → claude-3.5-sonnet → gpt-4o")
    print()
    print("⚙️  Configuration:")
    print("   • Default fallback model: claude-3.7-sonnet")
    print("   • Max retries: 1")
    print("   • Timeout: 10 minutes")

def main():
    """主函数"""
    print("🎯 Rate Limit Handling Demo")
    print("=" * 50)
    print("This demo showcases the automatic model downgrade feature")
    print("when encountering rate limits (429 errors).")
    print()
    print("Prerequisites:")
    print("• Server running on http://localhost:8080")
    print("• Valid GitHub Copilot authentication")
    print()
    
    # 显示错误处理信息
    show_error_handling_info()
    
    # 等待用户确认
    input("\nPress Enter to start the demo...")
    
    try:
        # 演示主要场景
        simulate_rate_limit_scenario()
        
        # 演示模型降级
        demonstrate_model_fallback()
        
        print("\n🎉 Demo completed!")
        print("=" * 50)
        print("Key takeaways:")
        print("• The system automatically handles rate limits")
        print("• Models are downgraded intelligently")
        print("• Users get responses even when primary models are unavailable")
        print("• The process is transparent and logged")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
