# 会话降级模型功能

## 🎯 功能概述

当在一个会话中遇到速率限制并使用了降级模型后，该会话的所有后续请求都将自动使用相同的降级模型，而不是每次都重新尝试原始模型。

## ✅ 解决的问题

### 问题场景
1. 用户请求 `claude-sonnet-4`
2. 遇到速率限制，系统降级到 `claude-3.7-sonnet`
3. 用户继续请求 `claude-sonnet-4`
4. **之前**：系统再次尝试 `claude-sonnet-4`，可能再次遇到速率限制
5. **现在**：系统直接使用 `claude-3.7-sonnet`，避免重复的速率限制错误

### 优势
- ✅ **避免重复错误**：不会反复触发速率限制
- ✅ **提升性能**：减少不必要的重试和错误处理
- ✅ **用户体验**：更快的响应时间，更稳定的服务
- ✅ **资源优化**：减少对 API 的无效请求

## 🔧 实现机制

### 会话状态管理

```kotlin
class ProxyServiceImpl {
    // 会话状态：记录当前会话使用的降级模型
    @Volatile
    private var sessionFallbackModel: String? = null
    
    // 会话状态：记录降级模型的使用时间
    @Volatile
    private var fallbackModelSetTime: Long = 0
}
```

### 模型选择逻辑

```kotlin
private fun selectBestModel(requestedModel: String): String {
    // 如果会话中已经设置了降级模型，优先使用降级模型
    sessionFallbackModel?.let { fallbackModel ->
        logger.info { "Using session fallback model: $fallbackModel" }
        return fallbackModel
    }
    
    // 否则按正常逻辑选择模型
    return normalModelSelection(requestedModel)
}
```

### 降级模型设置

```kotlin
private fun setSessionFallbackModel(fallbackModel: String) {
    sessionFallbackModel = fallbackModel
    fallbackModelSetTime = System.currentTimeMillis()
    logger.info { "Session fallback model set to: $fallbackModel" }
    logger.info { "All subsequent requests in this session will use: $fallbackModel" }
}
```

## 🔄 工作流程

### 正常情况（无速率限制）
```
1. 请求 claude-sonnet-4 → 成功使用 claude-sonnet-4
2. 请求 claude-sonnet-4 → 成功使用 claude-sonnet-4
3. 请求 gpt-4 → 成功使用 gpt-4
```

### 遇到速率限制后
```
1. 请求 claude-sonnet-4 → 速率限制 → 降级到 claude-3.7-sonnet ✅
   └── 设置会话降级模型: claude-3.7-sonnet

2. 请求 claude-sonnet-4 → 直接使用 claude-3.7-sonnet ✅
   └── 跳过原始模型，使用会话降级模型

3. 请求 gpt-4 → 仍然使用 claude-3.7-sonnet ✅
   └── 忽略请求的模型，使用会话降级模型

4. 请求 claude-3.5-sonnet → 仍然使用 claude-3.7-sonnet ✅
   └── 会话降级模型优先级最高
```

## 📊 日志示例

### 设置降级模型时
```
WARN  - Rate limit exceeded with model claude-sonnet-4: Rate limit exceeded: quota exceeded
INFO  - Attempting to use fallback model: claude-3.7-sonnet
INFO  - Session fallback model set to: claude-3.7-sonnet
INFO  - All subsequent requests in this session will use: claude-3.7-sonnet
```

### 使用会话降级模型时
```
INFO  - Using session fallback model: claude-3.7-sonnet (set at 2024-01-15T10:30:45Z)
```

## 🛠️ 管理接口

### 查询会话状态
```kotlin
// 获取当前会话的降级模型
fun getSessionFallbackModel(): String?

// 检查是否有活跃的会话降级模型
fun hasSessionFallbackModel(): Boolean
```

### 重置会话状态
```kotlin
// 清除会话降级模型（重置会话状态）
fun clearSessionFallbackModel()
```

## 🧪 测试验证

### 测试脚本
- `test_session_fallback.py` - 会话降级模型功能测试

### 测试场景
1. **正常流程测试**：验证无速率限制时的正常行为
2. **降级触发测试**：验证速率限制触发降级的行为
3. **会话持久性测试**：验证降级模型在会话中的持久性
4. **不同模型请求测试**：验证请求不同模型时仍使用降级模型

## 🔍 监控和调试

### 关键指标
- 会话降级模型设置次数
- 会话降级模型使用次数
- 避免的重复速率限制错误数量

### 调试信息
- 会话降级模型的设置时间
- 每次请求的模型选择决策过程
- 降级模型的使用统计

## ⚙️ 配置选项

### 可扩展的功能
1. **会话超时**：可以添加会话降级模型的超时机制
2. **自动重置**：在特定条件下自动重置会话状态
3. **模型优先级**：可以配置不同的降级策略
4. **会话隔离**：为不同用户/会话维护独立的状态

### 示例配置
```kotlin
// 可能的未来配置选项
class SessionConfig {
    val fallbackModelTimeout: Duration = Duration.ofHours(1)
    val autoResetOnSuccess: Boolean = false
    val maxFallbackDuration: Duration = Duration.ofDays(1)
}
```

## 🎯 使用建议

### 最佳实践
1. **监控会话状态**：定期检查会话降级模型的使用情况
2. **适时重置**：在合适的时机重置会话状态以尝试原始模型
3. **用户通知**：考虑在适当时候通知用户正在使用降级模型

### 注意事项
1. **模型差异**：降级模型可能在性能上有差异
2. **成本考虑**：不同模型可能有不同的成本结构
3. **功能限制**：某些功能可能只在特定模型上可用

## 🎉 总结

会话降级模型功能通过智能的状态管理，显著提升了系统在遇到速率限制时的用户体验和性能表现。一旦在会话中使用了降级模型，系统会记住这个选择并在后续请求中自动应用，避免了重复的错误和延迟。

这个功能是对原有速率限制处理机制的重要补充，使得整个系统更加智能和用户友好。
