#!/usr/bin/env python3
"""
测试 429 错误处理和模型降级功能
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8080"

def test_rate_limit_handling():
    """测试速率限制处理"""
    print("🧪 Testing Rate Limit Handling and Model Downgrade")
    print("=" * 60)
    
    # 测试 OpenAI API 格式
    print("\n📤 Testing OpenAI API format...")
    
    openai_request = {
        "model": "claude-sonnet-4",
        "messages": [
            {
                "role": "user", 
                "content": "Hello! This is a test for rate limit handling."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer test-token"
            },
            json=openai_request,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Model used: {result.get('model', 'unknown')}")
            print(f"📝 Response: {result.get('choices', [{}])[0].get('message', {}).get('content', '')[:100]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # 测试 Claude API 格式
    print("\n📤 Testing Claude API format...")
    
    claude_request = {
        "model": "claude-sonnet-4",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello! This is a test for rate limit handling with Claude format."
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=claude_request,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Model used: {result.get('model', 'unknown')}")
            print(f"📝 Response: {result.get('content', [{}])[0].get('text', '')[:100]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_model_selection():
    """测试模型选择逻辑"""
    print("\n🎯 Testing Model Selection Logic")
    print("=" * 60)
    
    test_cases = [
        ("claude-sonnet-4", "Should use Claude 4"),
        ("claude-3.7-sonnet", "Should use Claude 3.7"),
        ("claude-3.5-sonnet", "Should use Claude 3.5"),
        ("gpt-4", "Should map to gpt-4o"),
        ("gpt-3.5-turbo", "Should use gpt-3.5-turbo"),
        ("unknown-model", "Should default to Claude 4")
    ]
    
    for model, description in test_cases:
        print(f"\n🔍 Testing model: {model}")
        print(f"📝 Expected: {description}")
        
        request_data = {
            "model": model,
            "messages": [{"role": "user", "content": "Test model selection"}],
            "max_tokens": 10
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer test-token"
                },
                json=request_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                actual_model = result.get('model', 'unknown')
                print(f"✅ Actual model used: {actual_model}")
            else:
                print(f"❌ Error {response.status_code}: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")

def main():
    """主函数"""
    print("🚀 Starting Rate Limit Handling Tests")
    print("=" * 60)
    print("This script tests the 429 error handling and model downgrade functionality.")
    print("Make sure the server is running on http://localhost:8080")
    print()
    
    # 等待用户确认
    input("Press Enter to start testing...")
    
    try:
        # 测试基本功能
        test_rate_limit_handling()
        
        # 测试模型选择
        test_model_selection()
        
        print("\n🎉 All tests completed!")
        print("=" * 60)
        print("Check the server logs to see the rate limit handling in action.")
        print("If you see 429 errors, the system should automatically downgrade models.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")

if __name__ == "__main__":
    main()
