#!/usr/bin/env python3

import json
import requests
import time

def test_system_message_parsing():
    """测试 system 消息解析，特别是数组格式和 cache_control"""
    print("🔍 Testing System Message Parsing")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 检查服务器状态
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
            return False
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        return False
    
    print("━" * 60)
    
    # 测试 1: 简单字符串 system 消息
    print("📝 Test 1: Simple string system message")
    
    simple_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": "You are a helpful assistant.",
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ]
    }
    
    success1 = test_request(base_url, simple_system_request, "Simple String System")
    
    # 测试 2: 数组格式 system 消息（无 cache_control）
    print("\n📝 Test 2: Array system message (no cache_control)")
    
    array_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are a helpful assistant."
            },
            {
                "type": "text", 
                "text": "Always be polite and professional."
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ]
    }
    
    success2 = test_request(base_url, array_system_request, "Array System (No Cache)")
    
    # 测试 3: 数组格式 system 消息（带 cache_control）
    print("\n📝 Test 3: Array system message (with cache_control)")
    
    cache_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are Claude Code, Anthropic's official CLI for Claude.",
                "cache_control": {
                    "type": "ephemeral"
                }
            },
            {
                "type": "text",
                "text": "You are an interactive CLI tool that helps users with software engineering tasks.",
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ]
    }
    
    success3 = test_request(base_url, cache_system_request, "Array System (With Cache)")
    
    # 测试 4: 复杂的 system 消息（类似 Claude CLI）
    print("\n📝 Test 4: Complex system message (Claude CLI style)")
    
    complex_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are Claude Code, Anthropic's official CLI for Claude.",
                "cache_control": {
                    "type": "ephemeral"
                }
            },
            {
                "type": "text",
                "text": "\\nYou are an interactive CLI tool that helps users with software engineering tasks. Use the instructions below and the tools available to you to assist the user.\\n\\nIMPORTANT: Assist with defensive security tasks only.",
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": "What can you help me with?"
            }
        ]
    }
    
    success4 = test_request(base_url, complex_system_request, "Complex System (Claude CLI)")
    
    print("\n━" * 60)
    print("📊 System Message Parsing Test Results:")
    print(f"  ✅ Simple string system: {'PASS' if success1 else 'FAIL'}")
    print(f"  ✅ Array system (no cache): {'PASS' if success2 else 'FAIL'}")
    print(f"  ✅ Array system (with cache): {'PASS' if success3 else 'FAIL'}")
    print(f"  ✅ Complex system (Claude CLI): {'PASS' if success4 else 'FAIL'}")
    
    all_success = success1 and success2 and success3 and success4
    
    if all_success:
        print("\n🎉 All system message tests passed!")
        print("✅ System message parsing is working correctly!")
        print("✅ Both string and array formats are supported!")
    else:
        print("\n❌ Some system message tests failed.")
        print("Check server logs for parsing errors.")
        print("\n💡 Potential issues:")
        print("  • cache_control fields might be ignored")
        print("  • Array concatenation might not preserve formatting")
        print("  • Complex system messages might be truncated")
    
    return all_success

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    print(f"🚀 Testing {test_name}...")
    
    # 显示 system 消息信息
    system_msg = request_data.get('system')
    if isinstance(system_msg, str):
        print(f"🔧 System: String ({len(system_msg)} chars)")
        print(f"   Preview: {system_msg[:50]}{'...' if len(system_msg) > 50 else ''}")
    elif isinstance(system_msg, list):
        print(f"🔧 System: Array ({len(system_msg)} blocks)")
        for i, block in enumerate(system_msg):
            block_type = block.get('type', 'unknown')
            text = block.get('text', '')
            has_cache = 'cache_control' in block
            print(f"   {i+1}. Type: {block_type}, Cache: {has_cache}")
            print(f"      Text: {text[:40]}{'...' if len(text) > 40 else ''}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"📏 Response length: {len(text)} characters")
                    print(f"🤖 Response preview: {text[:60]}{'...' if len(text) > 60 else ''}")
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是 system 消息解析相关错误
            if any(keyword in error_text.lower() for keyword in [
                'system', 'parse', 'json', 'array', 'cache_control'
            ]):
                print("🔍 System message parsing error detected!")
                print(f"   Error: {error_text[:200]}")
                return False
            else:
                print("⚠️  Other server error")
                print(f"   Error: {error_text[:150]}")
                return False
                
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 System Message Parsing Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies system message parsing:")
    print("  ✅ Simple string format")
    print("  ✅ Array format without cache_control")
    print("  ✅ Array format with cache_control")
    print("  ✅ Complex Claude CLI style system messages")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_system_message_parsing()
    
    print("━" * 60)
    if success:
        print("🎉 SYSTEM MESSAGE PARSING TEST PASSED!")
        print("System message parsing is working correctly!")
    else:
        print("❌ System message parsing test failed.")
        print("Check server logs for parsing issues.")
