# 命令行参数支持功能

## 🎯 功能概述

为 GitHub Copilot LLM Provider 添加了命令行参数支持，允许用户通过命令行参数设置监听端口和主机地址。

## ✅ 实现的功能

### 1. 命令行参数支持

- `--port, -p <端口号>`: 设置监听端口（默认：8080）
- `--host, -h <主机地址>`: 设置绑定主机（默认：0.0.0.0）
- `--help`: 显示帮助信息

### 2. 配置优先级

1. **命令行参数** - 最高优先级
2. **环境变量** (`PORT`, `HOST`) - 中等优先级
3. **默认值** - 最低优先级

### 3. 向后兼容性

- 保持对现有环境变量的支持
- 不影响现有的 Docker 部署
- 默认行为保持不变

## 🔧 技术实现

### 1. 使用 Clikt 库

```kotlin
class GitHubCopilotLLMProvider : CliktCommand(
    name = "github-copilot-llm-provider",
    help = "GitHub Copilot LLM Provider - A proxy service for OpenAI and Claude API compatibility"
) {
    private val port by option(
        "--port", "-p",
        help = "Port to listen on (default: 8080)"
    ).int().default(8080)

    private val host by option(
        "--host", "-h", 
        help = "Host to bind to (default: 0.0.0.0)"
    ).default("0.0.0.0")
    
    // ...
}
```

### 2. 动态配置更新

```kotlin
object ServerConfig {
    private var _port: Int = System.getenv("PORT")?.toIntOrNull() ?: 8080
    private var _host: String = System.getenv("HOST") ?: "0.0.0.0"
    
    val port: Int get() = _port
    val host: String get() = _host
    
    fun updateFromCommandLine(port: Int, host: String) {
        _port = port
        _host = host
    }
}
```

## 📋 使用示例

### 1. 基本使用

```bash
# 默认端口 8080
java -jar build/libs/llm-provider.jar

# 自定义端口
java -jar build/libs/llm-provider.jar --port 9090

# 自定义主机和端口
java -jar build/libs/llm-provider.jar --host 127.0.0.1 --port 9090
```

### 2. 开发模式

```bash
# 使用 Gradle 运行
./gradlew run --args="--port 9090"

# 查看帮助
./gradlew run --args="--help"
```

### 3. Docker 使用

```bash
# 使用命令行参数
docker run -p 9090:9090 \
  github-copilot-llm-provider --port 9090

# 环境变量仍然有效
docker run -p 8080:8080 \
  -e PORT=8080 \
  github-copilot-llm-provider
```

## 🎉 帮助信息

```
Usage: github-copilot-llm-provider [<options>]

  GitHub Copilot LLM Provider - A proxy service for OpenAI and Claude API
  compatibility

Options:
  -p, --port=<int>   Port to listen on (default: 8080)
  -h, --host=<text>  Host to bind to (default: 0.0.0.0)
  --help             Show this message and exit
```

## ✅ 测试验证

### 1. 帮助信息测试

```bash
$ ./gradlew run --args="--help"
Usage: github-copilot-llm-provider [<options>]
...
```

### 2. 自定义端口测试

```bash
$ ./gradlew run --args="--port 9090"
# 服务器启动在端口 9090

$ curl http://localhost:9090/health
OK
```

### 3. API 功能测试

```bash
$ curl http://localhost:9090/
{
    "name": "GitHub Copilot LLM Provider",
    "version": "1.0.0",
    "description": "A proxy service for OpenAI and Claude API compatibility"
}
```

## 📚 文档更新

### 1. README.md 更新

- 添加了命令行参数说明
- 更新了运行示例
- 说明了配置优先级
- 添加了开发模式运行说明

### 2. Docker 支持

- 更新了 Dockerfile 注释
- 添加了 Docker 运行示例
- 说明了端口配置的不同方式

## 🔄 向后兼容性

### 保持的功能

- ✅ 环境变量 `PORT` 和 `HOST` 仍然有效
- ✅ 默认端口 8080 保持不变
- ✅ 现有的 Docker 部署不受影响
- ✅ 所有现有的 API 功能正常工作

### 新增的功能

- ✅ 命令行参数支持
- ✅ 帮助信息显示
- ✅ 更灵活的配置方式
- ✅ 更好的用户体验

## 🎯 使用场景

### 1. 开发环境

```bash
# 避免端口冲突
./gradlew run --args="--port 9090"
```

### 2. 生产部署

```bash
# 使用标准端口
java -jar app.jar --port 80

# 使用自定义配置
java -jar app.jar --host 0.0.0.0 --port 8080
```

### 3. 容器化部署

```bash
# 灵活的端口配置
docker run -p 9090:9090 image --port 9090
```

## 🎉 总结

这个功能增强了应用的灵活性和易用性：

- **更灵活的配置**：支持命令行参数、环境变量和默认值
- **更好的用户体验**：清晰的帮助信息和参数说明
- **向后兼容**：不影响现有的部署和使用方式
- **标准化**：使用业界标准的 Clikt 库实现命令行解析

用户现在可以根据需要选择最适合的配置方式，无论是开发、测试还是生产环境。
