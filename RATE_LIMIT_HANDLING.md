# 速率限制处理和模型降级功能

## 概述

本项目实现了智能的速率限制处理机制，当遇到 API 429 错误时，系统会自动降级到备用模型，确保服务的连续性和可用性。

## 功能特性

### 🔍 错误检测

系统能够检测以下类型的速率限制错误：

1. **直接 429 错误**
   - HTTP 状态码：429 Too Many Requests
   - 自动解析 `Retry-After` 头部信息

2. **间接 429 错误**
   - HTTP 状态码：500 Internal Server Error
   - 错误消息包含 "429 Too Many Requests"
   - 常见格式：`"Failed to process request: Chat completion failed: 429 Too Many Requests"`

### 🔄 模型降级策略

#### Claude 模型降级链

```
claude-sonnet-4 → claude-3.7-sonnet → claude-3.5-sonnet → claude-3-sonnet-20240229 → claude-3-haiku
```

#### 具体降级规则

1. **从 Claude 4 降级**
   ```
   claude-sonnet-4 → claude-3.7-sonnet (首选)
   ```

2. **从 Claude 3.7 降级**
   ```
   claude-3.7-sonnet → claude-3.5-sonnet → claude-3-sonnet-20240229
   ```

3. **跨模型系列降级**
   ```
   Claude 系列 → GPT 系列 (最后选择)
   ```

### ⚙️ 配置参数

```kotlin
// 默认降级模型
const val FALLBACK_MODEL_FOR_RATE_LIMIT = "claude-3.7-sonnet"

// 首选模型列表（按优先级排序）
val PREFERRED_CLAUDE_MODELS = listOf(
    "claude-sonnet-4",
    "claude-3.7-sonnet", 
    "claude-3.5-sonnet",
    "claude-3-sonnet-20240229",
    "claude-3-haiku"
)
```

## 实现细节

### 异常类型

```kotlin
/**
 * 速率限制异常 (429 Too Many Requests)
 */
class RateLimitException(
    message: String,
    val statusCode: Int = 429,
    val retryAfter: Long? = null,
    cause: Throwable? = null
) : Exception(message, cause)
```

### 核心处理逻辑

1. **错误检测**
   ```kotlin
   // 检查直接 429 错误
   if (response.status == HttpStatusCode.TooManyRequests) {
       throw RateLimitException(...)
   }
   
   // 检查间接 429 错误
   if (response.status == HttpStatusCode.InternalServerError) {
       if (errorBody.contains("429") && errorBody.contains("Too Many Requests")) {
           throw RateLimitException(...)
       }
   }
   ```

2. **模型降级**
   ```kotlin
   suspend fun getFallbackModelForRateLimit(apiToken: String, currentModel: String): String {
       // 获取可用模型列表
       val models = getSupportedModels(apiToken)
       val availableModels = models.data.map { it.id }
       
       // 根据当前模型选择合适的降级模型
       // ...
   }
   ```

3. **重试机制**
   ```kotlin
   private suspend fun executeWithRetryAndFallback(
       originalModel: String,
       operation: suspend (apiToken: String, model: String) -> T
   ): T {
       // 尝试原始模型
       // 如果遇到 RateLimitException，获取降级模型并重试
       // ...
   }
   ```

## 使用示例

### OpenAI 格式请求

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "claude-sonnet-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

### Claude 格式请求

```bash
curl -X POST http://localhost:8080/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-key" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-sonnet-4", 
    "max_tokens": 100,
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 日志输出

系统会记录详细的降级过程：

```
WARN  - Rate limit exceeded with model claude-sonnet-4: Rate limit exceeded: {...}
INFO  - Attempting to use fallback model: claude-3.7-sonnet
INFO  - Downgrading from claude-sonnet-4 to claude-3.7-sonnet due to rate limit
```

## 测试

### 运行测试

```bash
# 编译项目
./gradlew compileKotlin

# 运行演示脚本
python3 demo_rate_limit.py

# 运行测试脚本
python3 test_rate_limit_handling.py
```

### 测试场景

1. **正常请求流程**
   - 请求 Claude 4 → 成功返回

2. **速率限制处理**
   - 请求 Claude 4 → 429 错误 → 自动降级到 Claude 3.7 → 成功返回

3. **模型不可用处理**
   - 请求不存在的模型 → 自动选择最佳可用模型

## 监控和调试

### 关键指标

- 速率限制触发次数
- 模型降级成功率
- 降级后的响应时间
- 用户体验影响

### 调试信息

系统提供详细的调试输出，包括：
- 请求和响应的完整内容
- 模型选择和降级过程
- 错误处理和重试逻辑

## 最佳实践

1. **监控速率限制**
   - 定期检查日志中的 429 错误
   - 监控模型降级频率

2. **优化请求策略**
   - 合理设置请求间隔
   - 使用适当的模型选择策略

3. **用户体验**
   - 降级过程对用户透明
   - 保持响应质量和速度

## 故障排除

### 常见问题

1. **降级模型不可用**
   - 检查 GitHub Copilot 账户权限
   - 验证模型列表 API 响应

2. **持续 429 错误**
   - 检查请求频率
   - 考虑增加请求间隔

3. **降级失败**
   - 查看详细错误日志
   - 验证备用模型可用性
