#!/usr/bin/env python3
"""
测试 "quota exceeded" 错误处理功能
"""

import requests
import json
import time

def test_quota_exceeded_handling():
    """测试配额超限错误处理"""
    print("🧪 Testing Quota Exceeded Error Handling")
    print("=" * 60)
    print("This test verifies that the system correctly handles:")
    print('• "Rate limit exceeded: quota exceeded" errors')
    print("• Automatic model downgrade from Claude 4 to Claude 3.7")
    print("• Proper error detection in 500 responses")
    print()
    
    # 测试请求
    request_data = {
        "model": "claude-sonnet-4",
        "messages": [
            {
                "role": "user",
                "content": "This is a test to verify quota exceeded error handling."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    print("📤 Sending request that might trigger quota exceeded error...")
    print(f"🎯 Requested model: {request_data['model']}")
    print(f"💬 Message: {request_data['messages'][0]['content'][:50]}...")
    print()
    
    try:
        response = requests.post(
            "http://localhost:8080/v1/chat/completions",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer test-token"
            },
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"🤖 Model used: {result.get('model', 'unknown')}")
            
            # 检查是否发生了模型降级
            requested_model = request_data['model']
            actual_model = result.get('model', 'unknown')
            
            if actual_model != requested_model:
                print(f"🔄 Model downgrade detected:")
                print(f"   Requested: {requested_model}")
                print(f"   Actual: {actual_model}")
                print("   This indicates the system handled a rate limit error!")
            else:
                print(f"ℹ️  No model downgrade occurred (using {actual_model})")
                print("   This means no rate limit was encountered")
            
            print(f"📝 Response preview: {result.get('choices', [{}])[0].get('message', {}).get('content', '')[:100]}...")
            
        elif response.status_code == 500:
            print("❌ Server Error (500)")
            error_text = response.text
            print(f"📄 Error response: {error_text}")
            
            # 检查错误内容
            if "quota exceeded" in error_text.lower():
                print("🎯 Detected 'quota exceeded' in error response")
                print("   The system should automatically retry with a downgraded model")
            elif "rate limit" in error_text.lower():
                print("🎯 Detected 'rate limit' in error response")
                print("   The system should automatically retry with a downgraded model")
            else:
                print("ℹ️  No rate limit keywords detected in error")
                
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        print("   This might indicate the system is processing the request")
        print("   or handling rate limit retries")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error")
        print("   Make sure the server is running on localhost:8080")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_multiple_requests():
    """测试多个请求以可能触发速率限制"""
    print("\n🔄 Testing Multiple Requests to Trigger Rate Limits")
    print("=" * 60)
    print("Sending multiple requests quickly to potentially trigger rate limits...")
    print()
    
    request_data = {
        "model": "claude-sonnet-4",
        "messages": [{"role": "user", "content": "Quick test message"}],
        "max_tokens": 50
    }
    
    results = []
    
    for i in range(3):
        print(f"📤 Request {i+1}/3...")
        
        try:
            response = requests.post(
                "http://localhost:8080/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer test-token"
                },
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                model_used = result.get('model', 'unknown')
                results.append(f"✅ Success with {model_used}")
                print(f"   ✅ Success with {model_used}")
            else:
                results.append(f"❌ Error {response.status_code}")
                print(f"   ❌ Error {response.status_code}: {response.text[:50]}...")
                
        except Exception as e:
            results.append(f"❌ Exception: {str(e)[:30]}...")
            print(f"   ❌ Exception: {e}")
        
        # 短暂延迟
        time.sleep(0.5)
    
    print("\n📊 Summary of results:")
    for i, result in enumerate(results, 1):
        print(f"   {i}. {result}")

def show_error_patterns():
    """显示我们处理的错误模式"""
    print("\n📚 Error Patterns We Handle")
    print("=" * 60)
    print("Our system detects and handles these error patterns:")
    print()
    print("🔴 Direct 429 errors:")
    print('   HTTP 429 + "Too Many Requests"')
    print()
    print("🔴 500 errors with rate limit content:")
    print('   HTTP 500 + "429 Too Many Requests"')
    print('   HTTP 500 + "Rate limit exceeded: quota exceeded"')
    print('   HTTP 500 + "quota exceeded"')
    print('   HTTP 500 + "rate limiting"')
    print()
    print("🔄 Automatic actions:")
    print("   • Detect rate limit errors")
    print("   • Get fallback model (claude-3.7-sonnet)")
    print("   • Retry request with fallback model")
    print("   • Return successful response to user")

def main():
    """主函数"""
    print("🎯 Quota Exceeded Error Handling Test")
    print("=" * 60)
    print("This test verifies the system handles quota exceeded errors correctly.")
    print()
    print("Expected behavior:")
    print("• Detect 'quota exceeded' in error messages")
    print("• Automatically downgrade to claude-3.7-sonnet")
    print("• Retry and return successful response")
    print()
    
    # 显示错误模式
    show_error_patterns()
    
    # 等待用户确认
    input("\nPress Enter to start testing...")
    
    try:
        # 测试基本的配额超限处理
        test_quota_exceeded_handling()
        
        # 测试多个请求
        test_multiple_requests()
        
        print("\n🎉 Testing completed!")
        print("=" * 60)
        print("Check the server logs to see detailed error handling.")
        print("If quota limits are hit, you should see automatic model downgrades.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
