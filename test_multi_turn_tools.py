#!/usr/bin/env python3
"""
测试多轮会话中工具定义的传递
验证工具是否在多轮对话中被正确保持
"""

import requests
import json
import time

def test_multi_turn_tools():
    """测试多轮会话中的工具传递"""
    print("🧪 Testing Multi-turn Tool Persistence")
    print("=" * 60)
    print("This test verifies that tools are correctly passed in multi-turn conversations.")
    print()
    
    # 定义工具
    tools = [
        {
            "name": "read_file",
            "description": "Read the contents of a file",
            "input_schema": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "The path to the file to read"
                    }
                },
                "required": ["path"]
            }
        },
        {
            "name": "write_file",
            "description": "Write content to a file",
            "input_schema": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "The path to the file to write"
                    },
                    "content": {
                        "type": "string",
                        "description": "The content to write to the file"
                    }
                },
                "required": ["path", "content"]
            }
        }
    ]
    
    # 第一轮对话
    print("📤 Round 1: Initial request with tools...")
    request1 = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "Please read the file 'test.txt' and tell me what's in it."
            }
        ],
        "tools": tools
    }
    
    print(f"🛠️  Tools provided: {len(tools)} tools")
    for tool in tools:
        print(f"   - {tool['name']}: {tool['description']}")
    print()
    
    try:
        response1 = requests.post(
            "http://localhost:8080/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=request1,
            timeout=30
        )
        
        print(f"📊 Round 1 Status: {response1.status_code}")
        
        if response1.status_code == 200:
            result1 = response1.json()
            print("✅ Round 1 successful!")
            print(f"🤖 Model used: {result1.get('model', 'unknown')}")
            
            # 检查响应内容
            content1 = result1.get('content', [])
            has_tool_use = any(block.get('type') == 'tool_use' for block in content1)
            has_tool_text = any('[Tool:' in str(block.get('text', '')) for block in content1)
            
            print(f"🔍 Response analysis:")
            print(f"   Has tool_use blocks: {has_tool_use}")
            print(f"   Has [Tool: ...] text: {has_tool_text}")
            
            if has_tool_use:
                print("   ✅ Tools are working correctly in round 1")
                tool_use_blocks = [block for block in content1 if block.get('type') == 'tool_use']
                for block in tool_use_blocks:
                    print(f"   🔧 Tool call: {block.get('name')} (ID: {block.get('id')})")
            elif has_tool_text:
                print("   ❌ Tools are being converted to text instead of tool_use blocks")
            else:
                print("   ⚠️  No tool usage detected")
            
            # 第二轮对话 - 模拟工具结果
            print("\n📤 Round 2: Follow-up request with tool result...")
            
            # 构建第二轮请求，包含工具结果
            messages2 = [
                {
                    "role": "user",
                    "content": "Please read the file 'test.txt' and tell me what's in it."
                },
                {
                    "role": "assistant",
                    "content": result1.get('content', [])
                }
            ]
            
            # 如果第一轮有工具调用，添加工具结果
            if has_tool_use:
                tool_use_blocks = [block for block in content1 if block.get('type') == 'tool_use']
                if tool_use_blocks:
                    tool_id = tool_use_blocks[0].get('id')
                    messages2.append({
                        "role": "user",
                        "content": [
                            {
                                "type": "tool_result",
                                "tool_use_id": tool_id,
                                "content": "This is the content of test.txt file. It contains some sample text for testing purposes."
                            }
                        ]
                    })
            
            # 添加新的用户请求
            messages2.append({
                "role": "user",
                "content": "Now please write a summary of what you read to a new file called 'summary.txt'."
            })
            
            request2 = {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "messages": messages2,
                "tools": tools  # 重要：再次提供工具定义
            }
            
            print(f"🛠️  Tools provided again: {len(tools)} tools")
            print(f"💬 Messages in round 2: {len(messages2)} messages")
            print()
            
            response2 = requests.post(
                "http://localhost:8080/v1/messages",
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "test-key",
                    "anthropic-version": "2023-06-01"
                },
                json=request2,
                timeout=30
            )
            
            print(f"📊 Round 2 Status: {response2.status_code}")
            
            if response2.status_code == 200:
                result2 = response2.json()
                print("✅ Round 2 successful!")
                print(f"🤖 Model used: {result2.get('model', 'unknown')}")
                
                # 检查第二轮响应
                content2 = result2.get('content', [])
                has_tool_use_2 = any(block.get('type') == 'tool_use' for block in content2)
                has_tool_text_2 = any('[Tool:' in str(block.get('text', '')) for block in content2)
                
                print(f"🔍 Round 2 analysis:")
                print(f"   Has tool_use blocks: {has_tool_use_2}")
                print(f"   Has [Tool: ...] text: {has_tool_text_2}")
                
                if has_tool_use_2:
                    print("   ✅ Tools are working correctly in round 2")
                    tool_use_blocks_2 = [block for block in content2 if block.get('type') == 'tool_use']
                    for block in tool_use_blocks_2:
                        print(f"   🔧 Tool call: {block.get('name')} (ID: {block.get('id')})")
                elif has_tool_text_2:
                    print("   ❌ Tools are being converted to text in round 2")
                    print("   🚨 This indicates tools are being lost in multi-turn conversations!")
                else:
                    print("   ⚠️  No tool usage detected in round 2")
                
                return has_tool_use and has_tool_use_2
            else:
                print(f"❌ Round 2 failed: {response2.status_code}")
                print(f"📄 Error: {response2.text}")
                return False
        else:
            print(f"❌ Round 1 failed: {response1.status_code}")
            print(f"📄 Error: {response1.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_without_tools():
    """测试不提供工具时的行为"""
    print("\n🧪 Testing Without Tools (Control Test)")
    print("=" * 60)
    
    request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Please read the file 'test.txt' and tell me what's in it."
            }
        ]
        # 注意：没有提供 tools
    }
    
    print("📤 Sending request without tools...")
    print("🛠️  Tools provided: 0 tools")
    print()
    
    try:
        response = requests.post(
            "http://localhost:8080/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=request,
            timeout=20
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            content = result.get('content', [])
            has_tool_text = any('[Tool:' in str(block.get('text', '')) for block in content)
            
            if has_tool_text:
                print("❌ AI is trying to use tools even when none are provided!")
                print("   This suggests the AI is confused about tool availability")
            else:
                print("✅ AI correctly handles the absence of tools")
                
            # 显示响应内容
            for block in content:
                if block.get('type') == 'text':
                    text = block.get('text', '')
                    print(f"📝 Response: {text[:100]}...")
                    
            return not has_tool_text
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Multi-turn Tool Persistence Test")
    print("=" * 60)
    print("This test checks if tools are correctly maintained across multiple turns")
    print("in a conversation, and identifies if tools are being lost or converted to text.")
    print()
    
    # 等待用户确认
    input("Press Enter to start testing...")
    
    try:
        # 测试多轮工具传递
        multi_turn_success = test_multi_turn_tools()
        
        # 测试无工具情况
        no_tools_success = test_without_tools()
        
        print("\n🎉 Test Summary")
        print("=" * 60)
        
        if multi_turn_success:
            print("✅ Multi-turn tool persistence: PASSED")
        else:
            print("❌ Multi-turn tool persistence: FAILED")
            print("   Tools may be getting lost or converted to text in multi-turn conversations")
            
        if no_tools_success:
            print("✅ No-tools behavior: PASSED")
        else:
            print("❌ No-tools behavior: FAILED")
            print("   AI is trying to use tools when none are provided")
        
        if not multi_turn_success:
            print("\n🔍 Diagnosis:")
            print("The issue appears to be that tools are not being correctly")
            print("maintained or passed through in multi-turn conversations.")
            print("This could be due to:")
            print("• Tools not being included in follow-up requests")
            print("• Tool definitions being lost during message processing")
            print("• Incorrect tool format conversion between API formats")
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")

if __name__ == "__main__":
    main()
