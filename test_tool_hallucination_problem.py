#!/usr/bin/env python3
"""
测试工具调用幻觉问题

这个脚本测试当 assistant 消息中包含 tool_use 时，
系统是否会将其转换为 "[Tool: xxx]" 文本，
从而可能导致模型产生幻觉并直接回复这种格式。
"""

import requests
import json

def test_tool_hallucination():
    """测试工具调用是否会被转换为可能导致幻觉的文本格式"""
    
    print("🔍 测试工具调用幻觉问题")
    print("=" * 60)
    
    # 第一轮：正常的工具调用
    print("\n📤 第一轮：发送工具调用请求")
    
    response1 = requests.post(
        'http://localhost:8080/v1/messages',
        headers={
            'Content-Type': 'application/json',
            'x-api-key': 'test-key',
            'anthropic-version': '2023-06-01'
        },
        json={
            'model': 'claude-3.5-sonnet',
            'max_tokens': 100,
            'messages': [
                {
                    'role': 'user',
                    'content': 'Please list the files in the current directory.'
                }
            ],
            'tools': [
                {
                    'name': 'bash',
                    'description': 'Execute bash commands',
                    'input_schema': {
                        'type': 'object',
                        'properties': {
                            'command': {
                                'type': 'string',
                                'description': 'The bash command to execute'
                            }
                        },
                        'required': ['command']
                    }
                }
            ]
        }
    )
    
    if response1.status_code != 200:
        print(f"❌ 第一轮请求失败: {response1.status_code}")
        return
    
    result1 = response1.json()
    content1 = result1.get('content', [])
    
    print("📥 第一轮响应:")
    for i, block in enumerate(content1):
        print(f"  Block {i}: type={block.get('type')}, name={block.get('name')}, id={block.get('id')}")
    
    # 提取工具调用信息
    tool_use_block = None
    for block in content1:
        if block.get('type') == 'tool_use':
            tool_use_block = block
            break
    
    if not tool_use_block:
        print("❌ 第一轮没有返回工具调用")
        return
    
    tool_id = tool_use_block.get('id')
    tool_name = tool_use_block.get('name')
    
    print(f"✅ 第一轮成功返回工具调用: {tool_name} (ID: {tool_id})")
    
    # 第二轮：包含历史工具调用的对话
    print(f"\n📤 第二轮：发送包含历史工具调用的对话")
    print("   这将测试系统如何处理 assistant 消息中的 tool_use")
    
    response2 = requests.post(
        'http://localhost:8080/v1/messages',
        headers={
            'Content-Type': 'application/json',
            'x-api-key': 'test-key',
            'anthropic-version': '2023-06-01'
        },
        json={
            'model': 'claude-3.5-sonnet',
            'max_tokens': 150,
            'messages': [
                {
                    'role': 'user',
                    'content': 'Please list the files in the current directory.'
                },
                {
                    'role': 'assistant',
                    'content': [
                        {
                            'type': 'text',
                            'text': 'I\'ll list the files in the current directory for you.'
                        },
                        {
                            'type': 'tool_use',
                            'id': tool_id,
                            'name': tool_name,
                            'input': {'command': 'ls -la'}
                        }
                    ]
                },
                {
                    'role': 'user',
                    'content': [
                        {
                            'type': 'tool_result',
                            'tool_use_id': tool_id,
                            'content': 'total 24\ndrwxr-xr-x  5 <USER>  <GROUP>   160 Jan  1 12:00 .\ndrwxr-xr-x  3 <USER>  <GROUP>    96 Jan  1 12:00 ..\n-rw-r--r--  1 <USER>  <GROUP>   123 Jan  1 12:00 README.md\n-rw-r--r--  1 <USER>  <GROUP>   456 Jan  1 12:00 main.py'
                        }
                    ]
                },
                {
                    'role': 'user',
                    'content': 'Now please show me the content of README.md'
                }
            ],
            'tools': [
                {
                    'name': 'bash',
                    'description': 'Execute bash commands',
                    'input_schema': {
                        'type': 'object',
                        'properties': {
                            'command': {
                                'type': 'string',
                                'description': 'The bash command to execute'
                            }
                        },
                        'required': ['command']
                    }
                }
            ]
        }
    )
    
    if response2.status_code != 200:
        print(f"❌ 第二轮请求失败: {response2.status_code}")
        print(f"   错误信息: {response2.text}")
        return
    
    result2 = response2.json()
    content2 = result2.get('content', [])
    
    print("📥 第二轮响应:")
    for i, block in enumerate(content2):
        if block.get('type') == 'text':
            text_content = block.get('text', '')
            print(f"  Block {i}: type=text, content={text_content[:100]}...")
            
            # 检查是否包含 [Tool: xxx] 格式的文本
            if '[Tool:' in text_content or '[tool:' in text_content.lower():
                print(f"  ⚠️  发现可能的工具幻觉文本: {text_content}")
        elif block.get('type') == 'tool_use':
            print(f"  Block {i}: type=tool_use, name={block.get('name')}, id={block.get('id')}")
    
    # 分析结果
    print(f"\n📊 分析结果:")
    
    has_proper_tool_use = any(block.get('type') == 'tool_use' for block in content2)
    has_tool_hallucination = any(
        '[Tool:' in block.get('text', '') or '[tool:' in block.get('text', '').lower()
        for block in content2 if block.get('type') == 'text'
    )
    
    if has_proper_tool_use:
        print("✅ 第二轮正确返回了工具调用")
    elif has_tool_hallucination:
        print("❌ 第二轮出现了工具幻觉文本 - 模型直接回复了 [Tool: xxx] 格式")
        print("   这表明历史工具调用被错误地转换为文本发送给了模型")
    else:
        print("⚠️  第二轮没有工具调用，需要进一步分析")

if __name__ == "__main__":
    test_tool_hallucination()
