#!/usr/bin/env python3
"""
测试工具持久化问题
模拟客户端在多轮会话中忘记提供工具定义的情况
"""

import requests
import json
import time

def test_missing_tools_in_followup():
    """测试后续请求中缺少工具定义的问题"""
    print("🧪 Testing Missing Tools in Follow-up Requests")
    print("=" * 60)
    print("This test simulates the common issue where clients forget to")
    print("include tool definitions in follow-up requests after tool calls.")
    print()
    
    # 定义工具
    tools = [
        {
            "name": "read_file",
            "description": "Read the contents of a file",
            "input_schema": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "The path to the file to read"
                    }
                },
                "required": ["path"]
            }
        },
        {
            "name": "edit_file",
            "description": "Edit a file with new content",
            "input_schema": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "The path to the file to edit"
                    },
                    "content": {
                        "type": "string",
                        "description": "The new content for the file"
                    }
                },
                "required": ["path", "content"]
            }
        }
    ]
    
    # 第一轮：包含工具定义
    print("📤 Round 1: Request WITH tools...")
    request1 = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "Please read the file 'config.json' and then update it with new settings."
            }
        ],
        "tools": tools  # 包含工具定义
    }
    
    print(f"🛠️  Tools provided: {len(tools)} tools")
    print()
    
    try:
        response1 = requests.post(
            "http://localhost:8080/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=request1,
            timeout=30
        )
        
        print(f"📊 Round 1 Status: {response1.status_code}")
        
        if response1.status_code == 200:
            result1 = response1.json()
            print("✅ Round 1 successful!")
            
            # 检查是否有工具调用
            content1 = result1.get('content', [])
            tool_use_blocks = [block for block in content1 if block.get('type') == 'tool_use']
            
            if tool_use_blocks:
                print(f"🔧 Tool calls made: {len(tool_use_blocks)}")
                for block in tool_use_blocks:
                    print(f"   - {block.get('name')} (ID: {block.get('id')})")
                
                # 第二轮：模拟工具结果，但不包含工具定义
                print("\n📤 Round 2: Tool result WITHOUT tools (simulating client bug)...")
                
                # 构建包含工具结果的消息
                messages2 = [
                    {
                        "role": "user",
                        "content": "Please read the file 'config.json' and then update it with new settings."
                    },
                    {
                        "role": "assistant",
                        "content": content1
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "tool_result",
                                "tool_use_id": tool_use_blocks[0].get('id'),
                                "content": '{"database": {"host": "localhost", "port": 5432}, "cache": {"enabled": true}}'
                            }
                        ]
                    },
                    {
                        "role": "user",
                        "content": "Now please update the database port to 3306 and save the file."
                    }
                ]
                
                request2 = {
                    "model": "claude-3.5-sonnet",
                    "max_tokens": 200,
                    "messages": messages2
                    # 注意：故意不包含 tools 字段，模拟客户端忘记提供工具定义
                }
                
                print("🛠️  Tools provided: 0 tools (client forgot to include them)")
                print("⚠️  This simulates a common client-side bug")
                print()
                
                response2 = requests.post(
                    "http://localhost:8080/v1/messages",
                    headers={
                        "Content-Type": "application/json",
                        "x-api-key": "test-key",
                        "anthropic-version": "2023-06-01"
                    },
                    json=request2,
                    timeout=30
                )
                
                print(f"📊 Round 2 Status: {response2.status_code}")
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    print("✅ Round 2 successful!")
                    
                    # 检查响应内容
                    content2 = result2.get('content', [])
                    has_tool_use_2 = any(block.get('type') == 'tool_use' for block in content2)
                    has_tool_text_2 = any('[Tool:' in str(block.get('text', '')) for block in content2)
                    
                    print(f"🔍 Round 2 analysis:")
                    print(f"   Has tool_use blocks: {has_tool_use_2}")
                    print(f"   Has [Tool: ...] text: {has_tool_text_2}")
                    
                    if has_tool_use_2:
                        print("   ✅ AI correctly made tool calls despite missing tool definitions")
                        print("   (This would be unexpected but good)")
                    elif has_tool_text_2:
                        print("   ❌ AI is trying to use tools but they're converted to text")
                        print("   🚨 This confirms the tool persistence issue!")
                        
                        # 显示具体的文本内容
                        for block in content2:
                            if block.get('type') == 'text':
                                text = block.get('text', '')
                                if '[Tool:' in text:
                                    print(f"   📝 Tool text found: {text[:100]}...")
                    else:
                        print("   ⚠️  AI is not attempting to use tools")
                        print("   This could mean it's confused about tool availability")
                    
                    # 第三轮：重新提供工具定义
                    print("\n📤 Round 3: Request WITH tools again (fixing the issue)...")
                    
                    messages3 = messages2 + [
                        {
                            "role": "assistant",
                            "content": content2
                        },
                        {
                            "role": "user",
                            "content": "Let me try again. Please update the database port to 3306 and save the file."
                        }
                    ]
                    
                    request3 = {
                        "model": "claude-3.5-sonnet",
                        "max_tokens": 200,
                        "messages": messages3,
                        "tools": tools  # 重新提供工具定义
                    }
                    
                    print(f"🛠️  Tools provided: {len(tools)} tools (fixed)")
                    print()
                    
                    response3 = requests.post(
                        "http://localhost:8080/v1/messages",
                        headers={
                            "Content-Type": "application/json",
                            "x-api-key": "test-key",
                            "anthropic-version": "2023-06-01"
                        },
                        json=request3,
                        timeout=30
                    )
                    
                    print(f"📊 Round 3 Status: {response3.status_code}")
                    
                    if response3.status_code == 200:
                        result3 = response3.json()
                        print("✅ Round 3 successful!")
                        
                        content3 = result3.get('content', [])
                        has_tool_use_3 = any(block.get('type') == 'tool_use' for block in content3)
                        
                        if has_tool_use_3:
                            print("   ✅ AI correctly made tool calls after tools were provided again")
                            print("   🎯 This confirms that providing tools fixes the issue")
                        else:
                            print("   ⚠️  Still no tool calls even with tools provided")
                    
                    return has_tool_text_2  # Return True if we found the issue
                else:
                    print(f"❌ Round 2 failed: {response2.status_code}")
                    return False
            else:
                print("⚠️  No tool calls in round 1, can't test follow-up")
                return False
        else:
            print(f"❌ Round 1 failed: {response1.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Tool Persistence Issue Test")
    print("=" * 60)
    print("This test demonstrates the common issue where clients forget")
    print("to include tool definitions in follow-up requests, causing")
    print("the AI to output '[Tool: ...]' text instead of making actual tool calls.")
    print()
    
    # 等待用户确认
    input("Press Enter to start testing...")
    
    try:
        issue_found = test_missing_tools_in_followup()
        
        print("\n🎉 Test Summary")
        print("=" * 60)
        
        if issue_found:
            print("❌ Tool persistence issue CONFIRMED")
            print()
            print("🔍 Root Cause:")
            print("• Clients forget to include tool definitions in follow-up requests")
            print("• AI tries to use tools but they're not available")
            print("• System converts tool attempts to '[Tool: ...]' text")
            print()
            print("💡 Solutions:")
            print("1. Client-side: Always include tools in every request")
            print("2. Server-side: Cache and reuse tool definitions from previous requests")
            print("3. Server-side: Detect '[Tool: ...]' patterns and warn clients")
        else:
            print("✅ No tool persistence issue detected")
            print("Either the issue doesn't exist or the test conditions weren't met")
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")

if __name__ == "__main__":
    main()
