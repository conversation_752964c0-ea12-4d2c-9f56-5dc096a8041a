<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 隐藏框架和库的日志 -->
    <logger name="io.ktor" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    <logger name="io.ktor.routing" level="WARN"/>
    <logger name="io.ktor.server" level="WARN"/>
    <logger name="io.ktor.client" level="WARN"/>
    <logger name="kotlinx.coroutines" level="WARN"/>

    <!-- 隐藏详细的服务日志 -->
    <logger name="com.github.copilot.llmprovider.service.GitHubCopilotService" level="WARN"/>
    <logger name="com.github.copilot.llmprovider.service.ProxyServiceImpl" level="WARN"/>
    <logger name="com.github.copilot.llmprovider.auth" level="WARN"/>

    <!-- 只保留核心对话日志 -->
    <logger name="com.github.copilot.llmprovider.util.RequestResponseLogger" level="INFO"/>
    <logger name="com.github.copilot.llmprovider.cli" level="INFO"/>

    <root level="WARN">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
