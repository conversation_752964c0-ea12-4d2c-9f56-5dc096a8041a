package com.github.copilot.llmprovider.util

import mu.KotlinLogging
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

private val logger = KotlinLogging.logger {}

/**
 * 请求计时工具
 * 用于记录和打印各个阶段的耗时
 */
object RequestTimer {
    
    // 请求计数器，用于生成唯一的请求ID
    private val requestCounter = AtomicLong(0)
    
    // 存储每个请求的计时信息
    private val timings = ConcurrentHashMap<String, RequestTiming>()
    
    /**
     * 请求计时信息
     */
    data class RequestTiming(
        val requestId: String,
        val method: String,
        val path: String,
        val clientStartTime: Long,
        var proxyStartTime: Long? = null,
        var proxyEndTime: Long? = null,
        var clientEndTime: Long? = null,
        val details: MutableMap<String, Long> = mutableMapOf()
    ) {
        /**
         * 获取客户端总耗时（毫秒）
         */
        fun getClientDuration(): Long? {
            return if (clientEndTime != null) {
                clientEndTime!! - clientStartTime
            } else null
        }
        
        /**
         * 获取代理端耗时（毫秒）
         */
        fun getProxyDuration(): Long? {
            return if (proxyStartTime != null && proxyEndTime != null) {
                proxyEndTime!! - proxyStartTime!!
            } else null
        }
        
        /**
         * 添加详细计时点
         */
        fun addDetail(name: String, timestamp: Long = System.currentTimeMillis()) {
            details[name] = timestamp
        }
        
        /**
         * 获取两个计时点之间的耗时
         */
        fun getDurationBetween(start: String, end: String): Long? {
            val startTime = details[start]
            val endTime = details[end]
            return if (startTime != null && endTime != null) {
                endTime - startTime
            } else null
        }
    }
    
    /**
     * 开始记录客户端请求
     */
    fun startClientRequest(method: String, path: String): String {
        val requestId = "req_${requestCounter.incrementAndGet()}"
        val timing = RequestTiming(
            requestId = requestId,
            method = method,
            path = path,
            clientStartTime = System.currentTimeMillis()
        )
        timings[requestId] = timing
        
        logger.debug { "⏱️  Started client request: $requestId ($method $path)" }
        return requestId
    }
    
    /**
     * 开始记录代理请求
     */
    fun startProxyRequest(requestId: String) {
        timings[requestId]?.let { timing ->
            timing.proxyStartTime = System.currentTimeMillis()
            timing.addDetail("proxy_start", timing.proxyStartTime!!)
            logger.debug { "⏱️  Started proxy request: $requestId" }
        }
    }
    
    /**
     * 结束代理请求
     */
    fun endProxyRequest(requestId: String) {
        timings[requestId]?.let { timing ->
            timing.proxyEndTime = System.currentTimeMillis()
            timing.addDetail("proxy_end", timing.proxyEndTime!!)
            logger.debug { "⏱️  Ended proxy request: $requestId" }
        }
    }
    
    /**
     * 结束客户端请求并打印完整的计时信息
     */
    fun endClientRequest(requestId: String) {
        timings[requestId]?.let { timing ->
            timing.clientEndTime = System.currentTimeMillis()
            timing.addDetail("client_end", timing.clientEndTime!!)
            
            // 打印详细的计时信息
            printTimingReport(timing)
            
            // 清理已完成的请求
            timings.remove(requestId)
        }
    }
    
    /**
     * 添加自定义计时点
     */
    fun addTimingPoint(requestId: String, name: String) {
        timings[requestId]?.addDetail(name, System.currentTimeMillis())
    }
    
    /**
     * 打印计时报告
     */
    private fun printTimingReport(timing: RequestTiming) {
        val clientDuration = timing.getClientDuration()
        val proxyDuration = timing.getProxyDuration()
        
        println()
        println("⏱️  Request Timing Report")
        println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        println("🔍 Request ID: ${timing.requestId}")
        println("📋 Method: ${timing.method}")
        println("🎯 Path: ${timing.path}")
        println()
        
        // 客户端耗时
        if (clientDuration != null) {
            println("👤 Client Side:")
            println("   Total Duration: ${clientDuration}ms")
            println("   Start: ${formatTimestamp(timing.clientStartTime)}")
            println("   End: ${formatTimestamp(timing.clientEndTime!!)}")
        }
        
        println()
        
        // 代理端耗时
        if (proxyDuration != null) {
            println("🔄 Proxy Side:")
            println("   API Call Duration: ${proxyDuration}ms")
            println("   Start: ${formatTimestamp(timing.proxyStartTime!!)}")
            println("   End: ${formatTimestamp(timing.proxyEndTime!!)}")
        }
        
        // 计算其他耗时
        if (clientDuration != null && proxyDuration != null) {
            val overhead = clientDuration - proxyDuration
            val proxyPercentage = (proxyDuration.toDouble() / clientDuration * 100).toInt()
            val overheadPercentage = 100 - proxyPercentage
            
            println()
            println("📊 Performance Analysis:")
            println("   API Call: ${proxyDuration}ms (${proxyPercentage}%)")
            println("   Overhead: ${overhead}ms (${overheadPercentage}%)")
            
            // 性能评估
            when {
                overhead < 50 -> println("   ✅ Excellent performance (low overhead)")
                overhead < 200 -> println("   ⚡ Good performance")
                overhead < 500 -> println("   ⚠️  Moderate overhead")
                else -> println("   🐌 High overhead - needs optimization")
            }
        }
        
        // 详细计时点
        if (timing.details.isNotEmpty()) {
            println()
            println("🔍 Detailed Timings:")
            timing.details.entries.sortedBy { it.value }.forEach { (name, timestamp) ->
                val relativeTime = timestamp - timing.clientStartTime
                println("   $name: +${relativeTime}ms")
            }
        }
        
        println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        println()
    }
    
    /**
     * 格式化时间戳
     */
    private fun formatTimestamp(timestamp: Long): String {
        return java.time.Instant.ofEpochMilli(timestamp).toString()
    }
    
    /**
     * 获取当前活跃的请求数量
     */
    fun getActiveRequestCount(): Int = timings.size
    
    /**
     * 清理超时的请求（防止内存泄漏）
     */
    fun cleanupExpiredRequests(timeoutMs: Long = 5 * 60 * 1000L) { // 5分钟超时
        val now = System.currentTimeMillis()
        val expiredRequests = timings.filter { (_, timing) ->
            now - timing.clientStartTime > timeoutMs
        }
        
        expiredRequests.forEach { (requestId, timing) ->
            logger.warn { "Cleaning up expired request: $requestId (${timing.method} ${timing.path})" }
            timings.remove(requestId)
        }
    }
}
