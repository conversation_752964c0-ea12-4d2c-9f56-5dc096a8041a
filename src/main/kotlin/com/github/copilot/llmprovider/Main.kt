package com.github.copilot.llmprovider

import com.github.ajalt.clikt.core.CliktCommand
import com.github.ajalt.clikt.parameters.options.default
import com.github.ajalt.clikt.parameters.options.help
import com.github.ajalt.clikt.parameters.options.option
import com.github.ajalt.clikt.parameters.types.int
import com.github.copilot.llmprovider.auth.AuthManager
import com.github.copilot.llmprovider.cli.CliMonitor
import com.github.copilot.llmprovider.server.ServerConfig
import com.github.copilot.llmprovider.server.configureServer
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging

private val logger = KotlinLogging.logger {}

/**
 * 命令行应用程序类
 */
class GitHubCopilotLLMProvider : CliktCommand(
    name = "github-copilot-llm-provider",
    help = "GitHub Copilot LLM Provider - A proxy service for OpenAI and Claude API compatibility"
) {
    private val port by option(
        "--port", "-p",
        help = "Port to listen on (default: 8080)"
    ).int().default(8080)

    private val host by option(
        "--host", "-h",
        help = "Host to bind to (default: 0.0.0.0)"
    ).default("0.0.0.0")

    override fun run() {
        runBlocking {
            // 更新 ServerConfig 使用命令行参数
            ServerConfig.updateFromCommandLine(port, host)

            logger.info { "Starting GitHub Copilot LLM Provider..." }
            logger.info { "Server will listen on ${ServerConfig.host}:${ServerConfig.port}" }

            // 初始化认证管理器
            val authManager = AuthManager()

            try {
                // 初始化认证（这会检查现有配置或启动设备授权流程）
                logger.info { "Initializing GitHub Copilot authentication..." }
                authManager.initialize()

                // 启动 CLI 监控界面
                val cliMonitor = CliMonitor()
                val cliJob = launch {
                    cliMonitor.start()
                }

                // 启动 HTTP 服务器
                val server = embeddedServer(
                    Netty,
                    port = ServerConfig.port,
                    host = ServerConfig.host
                ) {
                    configureServer(authManager)
                }

                try {
                    server.start(wait = true)
                } catch (e: Exception) {
                    logger.error(e) { "Failed to start server" }
                    cliJob.cancel()
                    throw e
                }
            } catch (e: Exception) {
                logger.error(e) { "Failed to initialize authentication" }
                authManager.close()
                throw e
            }
        }
    }
}

/**
 * 应用程序主入口
 */
fun main(args: Array<String>) {
    GitHubCopilotLLMProvider().main(args)
}
