#!/usr/bin/env python3
"""
测试 tool_result 消息解析功能
验证 tool_use_id 和 content 能够正确合并成有意义的文本消息
"""

import requests
import json
import time

def test_tool_result_parsing():
    """测试 tool_result 消息解析"""
    print("🧪 Testing Tool Result Message Parsing")
    print("=" * 60)
    print("This test verifies that tool_result messages with tool_use_id and content")
    print("are correctly parsed and combined into meaningful text messages.")
    print()
    
    # 模拟包含 tool_result 的请求
    test_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "Please list the files in the directory."
            },
            {
                "role": "assistant",
                "content": [
                    {
                        "type": "tool_use",
                        "id": "call_jkTnLSj09UFHasqb4zGPykU4",
                        "name": "list_files",
                        "input": {"path": "/Users/<USER>/code/MyWorks/claude-code-prompts/"}
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "tool_use_id": "call_jkTnLSj09UFHasqb4zGPykU4",
                        "type": "tool_result",
                        "content": "- /Users/<USER>/code/MyWorks/claude-code-prompts/\n  - CLAUDE.md\n  - app/\n    - app/\n      - README.md\n      - index.html\n      - package.json\n      - public/\n        - vite.svg\n      - src/\n        - App.vue\n        - assets/\n          - vue.svg\n        - components/\n          - HelloWorld.vue\n        - main.js\n        - style.css\n      - vite.config.js\n  - system_prompt.md\n  - tools.md\n  - user_prompt.md\n\nNOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work."
                    }
                ]
            }
        ]
    }
    
    print("📤 Sending request with tool_result message...")
    print(f"🎯 Tool use ID: {test_request['messages'][2]['content'][0]['tool_use_id']}")
    print(f"📄 Content length: {len(test_request['messages'][2]['content'][0]['content'])} characters")
    print(f"📝 Content preview: {test_request['messages'][2]['content'][0]['content'][:100]}...")
    print()
    
    try:
        response = requests.post(
            "http://localhost:8080/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=test_request,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"🤖 Model used: {result.get('model', 'unknown')}")
            
            # 检查响应内容
            content = result.get('content', [])
            if content:
                for i, block in enumerate(content):
                    block_type = block.get('type', 'unknown')
                    print(f"📋 Content block {i+1}: {block_type}")
                    
                    if block_type == 'text':
                        text = block.get('text', '')
                        print(f"   Text length: {len(text)} characters")
                        print(f"   Text preview: {text[:150]}...")
                        
                        # 检查是否包含文件列表信息
                        if any(keyword in text.lower() for keyword in [
                            'claude.md', 'readme.md', 'package.json', 'app.vue'
                        ]):
                            print("   ✅ Response contains file information from tool_result!")
                        else:
                            print("   ⚠️  Response doesn't seem to reference the file list")
            
            return True
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Error details: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_simple_tool_result():
    """测试简单的 tool_result 消息"""
    print("\n🔧 Testing Simple Tool Result")
    print("=" * 60)
    
    simple_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "What's the weather like?"
            },
            {
                "role": "assistant", 
                "content": [
                    {
                        "type": "tool_use",
                        "id": "weather_123",
                        "name": "get_weather",
                        "input": {"location": "San Francisco"}
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "tool_use_id": "weather_123",
                        "type": "tool_result", 
                        "content": "The weather in San Francisco is sunny with a temperature of 72°F (22°C). Light breeze from the west at 5 mph."
                    }
                ]
            }
        ]
    }
    
    print("📤 Sending simple tool_result request...")
    print(f"🎯 Tool use ID: weather_123")
    print(f"📄 Weather data: {simple_request['messages'][2]['content'][0]['content']}")
    print()
    
    try:
        response = requests.post(
            "http://localhost:8080/v1/messages",
            headers={
                "Content-Type": "application/json",
                "x-api-key": "test-key",
                "anthropic-version": "2023-06-01"
            },
            json=simple_request,
            timeout=20
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Simple tool_result request successful!")
            
            content = result.get('content', [])
            if content and content[0].get('type') == 'text':
                text = content[0].get('text', '')
                print(f"📝 Response: {text[:200]}...")
                
                # 检查是否包含天气信息
                if any(keyword in text.lower() for keyword in [
                    'sunny', '72', 'san francisco', 'temperature', 'weather'
                ]):
                    print("   ✅ Response incorporates weather data from tool_result!")
                    return True
                else:
                    print("   ⚠️  Response doesn't seem to use the weather data")
                    return False
            else:
                print("   ⚠️  No text content in response")
                return False
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def show_expected_behavior():
    """显示期望的行为"""
    print("\n📚 Expected Tool Result Parsing Behavior")
    print("=" * 60)
    print("🔄 Before fix:")
    print("   tool_result message → '[Tool Result: call_jkTnLSj09UFHasqb4zGPykU4]'")
    print("   ❌ Problem: Content is lost, only ID is preserved")
    print()
    print("🚀 After fix:")
    print("   tool_result message → 'Tool result for call_jkTnLSj09UFHasqb4zGPykU4: [actual content]'")
    print("   ✅ Solution: Both tool_use_id and content are preserved and combined")
    print()
    print("📋 Example transformation:")
    print("   Input:")
    print("   {")
    print('     "tool_use_id": "call_123",')
    print('     "type": "tool_result",')
    print('     "content": "Files: file1.txt, file2.py"')
    print("   }")
    print()
    print("   Output:")
    print('   "Tool result for call_123: Files: file1.txt, file2.py"')

def main():
    """主函数"""
    print("🎯 Tool Result Message Parsing Test")
    print("=" * 60)
    print("This test verifies that tool_result messages are correctly parsed")
    print("to combine tool_use_id and content into meaningful text messages.")
    print()
    
    # 显示期望行为
    show_expected_behavior()
    
    # 等待用户确认
    input("\nPress Enter to start testing...")
    
    try:
        # 测试复杂的 tool_result
        success1 = test_tool_result_parsing()
        
        # 测试简单的 tool_result
        success2 = test_simple_tool_result()
        
        print("\n🎉 Testing Summary")
        print("=" * 60)
        
        if success1:
            print("✅ Complex tool_result parsing: PASSED")
        else:
            print("❌ Complex tool_result parsing: FAILED")
            
        if success2:
            print("✅ Simple tool_result parsing: PASSED")
        else:
            print("❌ Simple tool_result parsing: FAILED")
        
        if success1 and success2:
            print("\n🎉 All tests passed! Tool result parsing is working correctly.")
        else:
            print("\n⚠️  Some tests failed. Check the implementation.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
